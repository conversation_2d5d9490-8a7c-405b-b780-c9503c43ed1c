import 'dart:convert';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:ui_controls_library/widgets/utils/font_manager.dart';

/// A segmented progress indicator widget for password strength
class SegmentedProgressIndicator extends StatelessWidget {
  final int strength;
  final int maxSegments;
  final double segmentWidth;
  final double segmentHeight;
  final double segmentSpacing;
  final List<Color> strengthColors;
  final String? strengthLabel;
  final bool showLabel;

  const SegmentedProgressIndicator({
    super.key,
    required this.strength,
    this.maxSegments = 5,
    this.segmentWidth = 20.0,
    this.segmentHeight = 4.0,
    this.segmentSpacing = 2.0,
    this.strengthColors = const [
      Color(0xFFE0E0E0), // Empty
      Color(0xFFFF4444), // Weak - Red
      Color(0xFFFF8800), // Fair - Orange
      //Color(0xFFFFCC00), // Good - Yellow
      Color(0xFF00AA00), // Strong - Green
    ],
    this.strengthLabel,
    this.showLabel = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Segments row that takes full width
        Row(
          children: List.generate(maxSegments, (index) {
            final isActive = index < strength;
            final color =
                isActive
                    ? _getSegmentColor(strength)
                    : strengthColors[0]; // Empty color

            return Expanded(
              child: Container(
                margin: EdgeInsets.only(
                  right: index < maxSegments - 1 ? segmentSpacing : 0,
                ),
                height: segmentHeight,
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(segmentHeight / 2),
                ),
              ),
            );
          }),
        ),

        // Label below segments
        if (showLabel && strengthLabel != null) ...[
          const SizedBox(height: 4),
          Align(
            alignment: Alignment.centerRight,
            child: Text(
              strengthLabel!,
              // style: TextStyle(
              //   fontSize: 12,
              //   fontWeight: FontWeight.w500,
              //   color: _getSegmentColor(strength),
              // ),
              style: FontManager.getCustomStyle(
                fontFamily: FontManager.fontFamilyInter,
                fontWeight: FontManager.medium,
                fontSize: 12,
                color: _getSegmentColor(strength),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Color _getSegmentColor(int strength) {
    if (strength <= 0) return strengthColors[0];
    if (strength == 1) return strengthColors[1]; // Red
    if (strength <= 2) return strengthColors[2]; // Orange
    if (strength <= 3) return strengthColors[3]; // Yellow
    return strengthColors[4]; // Green
  }
}

/// A comprehensive password input widget with 7-case design pattern
class PasswordWidget extends StatefulWidget {
  // Basic properties
  final String initialValue;
  final bool isRequired;
  final int? minLength;
  final int? maxLength;
  final bool showLabel;

  // Password specific properties
  final bool showStrengthIndicator;
  final bool showRequirements;
  final bool enforceRequirements;
  final bool requireUppercase;
  final bool requireLowercase;
  final bool requireNumbers;
  final bool requireSpecialChars;
  final int minStrength;
  final List<String> customRequirements;
  final String obscuringCharacter;
  final bool initiallyObscured;

  // Appearance properties
  final Color textColor;
  final Color backgroundColor;
  final Color borderColor;
  final double borderWidth;
  final double borderRadius;
  final bool hasBorder;
  final double fontSize;
  final FontWeight fontWeight;
  final bool isCompact;
  final bool hasShadow;
  final double elevation;
  final bool isDarkTheme;
  final TextAlign textAlign;

  // Strength indicator properties
  final List<Color> strengthColors;
  final double strengthIndicatorHeight;
  final bool showStrengthText;
  final List<String> strengthLabels;

  // Label properties
  final String? label;
  final String? hint;
  final String? helperText;
  final String? errorText;

  // Icon properties
  final bool showPrefix;
  final IconData? prefixIcon;
  final bool showSuffix;
  final IconData? suffixIcon;
  final bool showToggleVisibility;
  final bool showCopyButton;
  final bool showGenerateButton;

  // Behavior properties
  final bool isReadOnly;
  final bool isDisabled;
  final bool autofocus;
  final bool hasAnimation;
  final bool autoValidate;
  final bool showClearButton;
  final bool enableCopyPaste;
  final bool confirmOnSubmit;

  // Layout properties
  final double width;
  final double height;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;

  // Callbacks
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;
  final Function(bool)? onVisibilityChanged;
  final Function(int)? onStrengthChanged;
  final Function()? onGenerate;

  // Advanced Interaction Properties
  final void Function(bool)? onHover;
  final void Function(bool)? onFocus;
  final Color? hoverColor;
  final Color? focusColor;
  final String? tooltip;
  final String? semanticsLabel;
  final bool enableFeedback;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;

  // JSON Configuration Properties
  final dynamic passwordPolicyJson;
  final bool validateWithJsonPolicy;
  final bool showJsonPolicyRequirements;
  final bool useJsonPolicyStrengthCalculation;
  final bool useJsonPolicyForGeneration;
  final bool highlightJsonPolicyRequirements;
  final Color metRequirementColor;
  final Color unmetRequirementColor;
  final bool showPasswordExpiry;
  final bool showPasswordHistory;
  final bool showBreachDetection;

  const PasswordWidget({
    super.key,
    this.initialValue = '',
    this.isRequired = false,
    this.minLength = 8,
    this.maxLength = 32,
    this.showLabel = true,
    this.showStrengthIndicator = true,
    this.showRequirements = false, // Changed to false for 7-case design
    this.enforceRequirements = true,
    this.requireUppercase = true,
    this.requireLowercase = true,
    this.requireNumbers = true,
    this.requireSpecialChars = true,
    this.minStrength = 3,
    this.customRequirements = const [],
    this.obscuringCharacter = '•',
    this.initiallyObscured = true,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = const Color(0xFFCCCCCC),
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.hasBorder = true,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.isCompact = false,
    this.hasShadow = false, // Changed to false to remove shadow
    this.elevation = 2.0,
    this.isDarkTheme = false,
    this.textAlign = TextAlign.start,
    this.strengthColors = const [
      Colors.red,
      Colors.orange,
      Colors.yellow,
      Colors.lightGreen,
      Colors.green,
    ],
    this.strengthIndicatorHeight = 4.0,
    this.showStrengthText = true,
    this.strengthLabels = const ['Very Weak', 'Weak', 'Fair', 'Strong'],
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.showPrefix = false, // Changed to false for clean design
    this.prefixIcon = Icons.lock,
    this.showSuffix = false,
    this.suffixIcon,
    this.showToggleVisibility = true,
    this.showCopyButton = false,
    this.showGenerateButton = false,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.autofocus = false,
    this.hasAnimation = false,
    this.autoValidate = true,
    this.showClearButton = false,
    this.enableCopyPaste = true,
    this.confirmOnSubmit = false,
    this.width = double.infinity,
    this.height = 0,
    this.padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
    this.margin = const EdgeInsets.all(0),
    this.onChanged,
    this.onSubmitted,
    this.onVisibilityChanged,
    this.onStrengthChanged,
    this.onGenerate,
    // Advanced Interaction Properties
    this.onHover,
    this.onFocus,
    this.hoverColor,
    this.focusColor,
    this.tooltip,
    this.semanticsLabel,
    this.enableFeedback = true,
    this.onDoubleTap,
    this.onLongPress,
    // JSON Configuration Properties
    this.passwordPolicyJson,
    this.validateWithJsonPolicy = false,
    this.showJsonPolicyRequirements = false,
    this.useJsonPolicyStrengthCalculation = false,
    this.useJsonPolicyForGeneration = false,
    this.highlightJsonPolicyRequirements = true,
    this.metRequirementColor = Colors.green,
    this.unmetRequirementColor = Colors.red,
    this.showPasswordExpiry = false,
    this.showPasswordHistory = false,
    this.showBreachDetection = false,
  });

  /// Creates a PasswordWidget from a JSON map
  factory PasswordWidget.fromJson(Map<String, dynamic> json) {
    return PasswordWidget(
      initialValue: json['initialValue']?.toString() ?? '',
      isRequired: json['isRequired'] == true,
      minLength:
          json['minLength'] != null
              ? int.tryParse(json['minLength'].toString())
              : null,
      maxLength:
          json['maxLength'] != null
              ? int.tryParse(json['maxLength'].toString())
              : null,
      showStrengthIndicator: json['showStrengthIndicator'] != false,
      showRequirements: json['showRequirements'] == true,
      enforceRequirements: json['enforceRequirements'] != false,
      requireUppercase: json['requireUppercase'] != false,
      requireLowercase: json['requireLowercase'] != false,
      requireNumbers: json['requireNumbers'] != false,
      requireSpecialChars: json['requireSpecialChars'] != false,
      minStrength:
          json['minStrength'] != null
              ? int.tryParse(json['minStrength'].toString()) ?? 3
              : 3,
      obscuringCharacter: json['obscuringCharacter']?.toString() ?? '•',
      initiallyObscured: json['initiallyObscured'] != false,
      textColor: _parseColor(json['textColor']) ?? Colors.black,
      backgroundColor: _parseColor(json['backgroundColor']) ?? Colors.white,
      borderColor: _parseColor(json['borderColor']) ?? const Color(0xFFCCCCCC),
      borderWidth:
          json['borderWidth'] != null
              ? double.tryParse(json['borderWidth'].toString()) ?? 1.0
              : 1.0,
      borderRadius:
          json['borderRadius'] != null
              ? double.tryParse(json['borderRadius'].toString()) ?? 4.0
              : 4.0,
      hasBorder: json['hasBorder'] != true,
      fontSize:
          json['fontSize'] != null
              ? double.tryParse(json['fontSize'].toString()) ?? 16.0
              : 16.0,
      fontWeight: _parseFontWeight(json['fontWeight']),
      isCompact: json['isCompact'] == true,
      hasShadow: json['hasShadow'] == true,
      elevation:
          json['elevation'] != null
              ? double.tryParse(json['elevation'].toString()) ?? 2.0
              : 2.0,
      isDarkTheme: json['isDarkTheme'] == true,
      textAlign: _parseTextAlign(json['textAlign']),
      strengthIndicatorHeight:
          json['strengthIndicatorHeight'] != null
              ? double.tryParse(json['strengthIndicatorHeight'].toString()) ??
                  4.0
              : 4.0,
      showStrengthText: json['showStrengthText'] != false,
      label: json['label']?.toString(),
      hint: json['hint']?.toString(),
      helperText: json['helperText']?.toString(),
      errorText: json['errorText']?.toString(),
      showPrefix: json['showPrefix'] == true,
      showSuffix: json['showSuffix'] == true,
      showToggleVisibility: json['showToggleVisibility'] != false,
      showCopyButton: json['showCopyButton'] == true,
      showGenerateButton: json['showGenerateButton'] == true,
      isReadOnly: json['isReadOnly'] == true,
      isDisabled: json['isDisabled'] == true,
      autofocus: json['autofocus'] == true,
      hasAnimation: json['hasAnimation'] == true,
      autoValidate: json['autoValidate'] != false,
      showClearButton: json['showClearButton'] == true,
      enableCopyPaste: json['enableCopyPaste'] != false,
      confirmOnSubmit: json['confirmOnSubmit'] == true,
      width:
          json['width'] != null
              ? double.tryParse(json['width'].toString()) ?? double.infinity
              : double.infinity,
      height:
          json['height'] != null
              ? double.tryParse(json['height'].toString()) ?? 0.0
              : 0.0,
      hoverColor: _parseColor(json['hoverColor']),
      focusColor: _parseColor(json['focusColor']),
      tooltip: json['tooltip']?.toString(),
      semanticsLabel: json['semanticsLabel']?.toString(),
      enableFeedback: json['enableFeedback'] != false,
      validateWithJsonPolicy: json['validateWithJsonPolicy'] == true,
      showJsonPolicyRequirements: json['showJsonPolicyRequirements'] == true,
      useJsonPolicyStrengthCalculation:
          json['useJsonPolicyStrengthCalculation'] == true,
      useJsonPolicyForGeneration: json['useJsonPolicyForGeneration'] == true,
      highlightJsonPolicyRequirements:
          json['highlightJsonPolicyRequirements'] != false,
      metRequirementColor:
          _parseColor(json['metRequirementColor']) ?? Colors.green,
      unmetRequirementColor:
          _parseColor(json['unmetRequirementColor']) ?? Colors.red,
      showPasswordExpiry: json['showPasswordExpiry'] == true,
      showPasswordHistory: json['showPasswordHistory'] == true,
      showBreachDetection: json['showBreachDetection'] == true,
    );
  }

  /// Helper method to parse colors from JSON
  static Color? _parseColor(dynamic colorValue) {
    if (colorValue == null) return null;

    if (colorValue is String) {
      if (colorValue.startsWith('#')) {
        try {
          final hexValue = int.parse(colorValue.substring(1), radix: 16);
          if (colorValue.length == 7) {
            return Color(0xFF000000 | hexValue);
          } else if (colorValue.length == 9) {
            return Color(hexValue);
          }
        } catch (e) {
          return null;
        }
      }

      switch (colorValue.toLowerCase()) {
        case 'red':
          return Colors.red;
        case 'blue':
          return Colors.blue;
        case 'green':
          return Colors.green;
        case 'yellow':
          return Colors.yellow;
        case 'orange':
          return Colors.orange;
        case 'purple':
          return Colors.purple;
        case 'pink':
          return Colors.pink;
        case 'brown':
          return Colors.brown;
        case 'grey':
        case 'gray':
          return Colors.grey;
        case 'black':
          return Colors.black;
        case 'white':
          return Colors.white;
        default:
          return null;
      }
    } else if (colorValue is int) {
      return Color(colorValue);
    }

    return null;
  }

  /// Helper method to parse font weight from JSON
  static FontWeight _parseFontWeight(dynamic weightValue) {
    if (weightValue == null) return FontWeight.normal;

    if (weightValue is String) {
      switch (weightValue.toLowerCase()) {
        case 'bold':
          return FontWeight.bold;
        case 'normal':
          return FontWeight.normal;
        case 'light':
          return FontWeight.w300;
        default:
          return FontWeight.normal;
      }
    } else if (weightValue is int) {
      final weight = (weightValue ~/ 100) * 100;
      return FontWeight.values.firstWhere(
        (w) => w.index == weight ~/ 100 - 1,
        orElse: () => FontWeight.normal,
      );
    }

    return FontWeight.normal;
  }

  /// Helper method to parse text alignment from JSON
  static TextAlign _parseTextAlign(dynamic alignValue) {
    if (alignValue == null) return TextAlign.start;

    switch (alignValue.toString().toLowerCase()) {
      case 'center':
        return TextAlign.center;
      case 'end':
      case 'right':
        return TextAlign.end;
      case 'justify':
        return TextAlign.justify;
      case 'left':
      case 'start':
      default:
        return TextAlign.start;
    }
  }

  @override
  State<PasswordWidget> createState() => _PasswordWidgetState();
}

class _PasswordWidgetState extends State<PasswordWidget>
    with SingleTickerProviderStateMixin {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  late AnimationController _animationController;
  late Animation<double> _animation;

  String? _errorText;
  bool _obscureText = true;
  int _passwordStrength = 0;
  List<String> _failedRequirements = [];
  bool _isHovered = false;
  bool _hasFocus = false;
  bool _isEntering = false;
  bool _showSuggestions = false;

  // Regular expressions for password validation
  final _uppercaseRegex = RegExp(r'[A-Z]');
  final _lowercaseRegex = RegExp(r'[a-z]');
  final _numberRegex = RegExp(r'[0-9]');
  final _specialCharRegex = RegExp(r'[!@#$%^&*(),.?":{}|<>]');

  @override
  void initState() {
    super.initState();

    // Initialize controller with initial value
    _controller = TextEditingController(text: widget.initialValue);

    // Initialize focus node
    _focusNode = FocusNode();
    _focusNode.addListener(_handleFocusChange);

    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Create animation
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Set initial error text
    _errorText = widget.errorText;

    // Set initial obscure text state
    _obscureText = widget.initiallyObscured;

    // Calculate initial password strength
    if (widget.initialValue.isNotEmpty) {
      _calculatePasswordStrength(widget.initialValue);
    }
  }

  /// Handles focus changes
  void _handleFocusChange() {
    setState(() {
      _hasFocus = _focusNode.hasFocus;
      _isEntering = _hasFocus;
      _showSuggestions = _hasFocus && _controller.text.isEmpty;
    });

    if (widget.onFocus != null) {
      widget.onFocus!(_hasFocus);
    }
  }

  /// Handles hover changes
  void _handleHoverChange(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
    });

    if (widget.onHover != null) {
      widget.onHover!(isHovered);
    }
  }

  /// Handles changes to the input
  void _handleValueChanged(String value) {
    setState(() {
      _isEntering = value.isNotEmpty;
      _showSuggestions = _hasFocus && value.isEmpty;
    });

    _calculatePasswordStrength(value);

    if (widget.onChanged != null) {
      widget.onChanged!(value);
    }
  }

  /// Toggles the visibility of the password
  void _toggleVisibility() {
    setState(() {
      _obscureText = !_obscureText;
    });

    if (widget.onVisibilityChanged != null) {
      widget.onVisibilityChanged!(_obscureText);
    }
  }

  /// Gets the strength label based on strength level
  String _getStrengthLabel(int strength) {
    switch (strength) {
      case 1:
        return 'Weak';
      case 2:
        return 'Fair';
      case 3:
        return 'Strong';
      default:
        return '';
    }
  }

  /// Gets border color based on state
  Color _getBorderColor() {
    if (_hasFocus) {
      return const Color(0xFF0058FF);
    } else if (_isHovered) {
      return const Color(0xFF0058FF);
    } else {
      return const Color(0xFFCCCCCC);
    }
  }

  /// Builds the suggestions dropdown with strength indicator
  Widget _buildSuggestions() {
    if (!_showSuggestions) return const SizedBox.shrink();

    final suggestions = [
      'At Least One Lowercase',
      'Canada (+32)',
      'Colombia (+73)',
      'Costa Rica (+54)',
    ];

    return Transform.translate(
      offset: const Offset(0, -18),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Gray strength indicator when showing suggestions - ABOVE suggestions
          if (widget.showStrengthIndicator) ...[
            SegmentedProgressIndicator(
              strength: 3, // Show all segments
              maxSegments: 3,
              segmentWidth: 50,
              segmentHeight: 4,
              segmentSpacing: 2,
              strengthColors: const [
                Color(0xFFE0E0E0), // Gray for all segments
                Color(0xFFE0E0E0),
                Color(0xFFE0E0E0),
                Color(0xFFE0E0E0),
                Color(0xFFE0E0E0),
              ],
              strengthLabel: '', // No label when showing suggestions
              showLabel: false,
            ),
            const SizedBox(height: 8),
          ],
          // Suggestions dropdown
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: const Color(0xFFE0E0E0)),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.all(12),
                  child: Text(
                    'Suggestions',
                    //style: TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
                    style: FontManager.getCustomStyle(
                      fontFamily: FontManager.fontFamilyInter,
                      fontWeight: FontManager.semiBold,
                      fontSize: _getResponsiveValueFontSize(context),
                    ),
                  ),
                ),
                ...suggestions.map(
                  (suggestion) => InkWell(
                    onTap: () {
                      // Handle suggestion tap
                    },
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      child: Row(
                        children: [
                          Container(
                            width: 4,
                            height: 4,
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            suggestion,
                            //style: const TextStyle(fontSize: 14),
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontWeight: FontManager.medium,
                              fontSize: _getResponsiveValueFontSize(context),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the strength indicator below the input field
  Widget _buildStrengthIndicator() {
    if (!widget.showStrengthIndicator) {
      return const SizedBox.shrink();
    }

    // Don't show strength indicator when suggestions are showing (it's handled in suggestions)
    if (_showSuggestions) {
      return const SizedBox.shrink();
    }

    // Normal strength indicator when not showing suggestions and has content
    if (_controller.text.isEmpty) {
      return const SizedBox.shrink();
    }

    return Transform.translate(
      offset: const Offset(0, -20),
      child: SegmentedProgressIndicator(
        strength: _passwordStrength,
        maxSegments: 3,
        segmentWidth: 50,
        segmentHeight: 4,
        segmentSpacing: 2,
        strengthLabel: _getStrengthLabel(_passwordStrength),
        showLabel: true,
      ),
    );
  }

  /// Calculates the password strength (0-3 for 3 segments)
  void _calculatePasswordStrength(String password) {
    if (password.isEmpty) {
      setState(() {
        _passwordStrength = 0;
      });
      return;
    }

    // Standard strength calculation
    int strength = 0;

    // Length check
    if (password.length >= 6) strength++;
    if (password.length >= 8) strength++;

    // Character variety check
    if (_uppercaseRegex.hasMatch(password)) strength++;
    if (_lowercaseRegex.hasMatch(password)) strength++;
    if (_numberRegex.hasMatch(password)) strength++;
    if (_specialCharRegex.hasMatch(password)) strength++;

    // Map to 1-3 scale for 3 segments
    if (strength <= 2)
      strength = 1; // Weak
    else if (strength <= 4)
      strength = 2; // Fair
    else
      strength = 3; // Strong

    setState(() {
      _passwordStrength = strength;
    });

    if (widget.onStrengthChanged != null) {
      widget.onStrengthChanged!(strength);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Create suffix icons - only visibility toggle, no inline progress indicator
    List<Widget> suffixIcons = [];

    // if (widget.showToggleVisibility) {
    //   suffixIcons.add(
    //     IconButton(
    //       icon: Icon(
    //         _obscureText ? Icons.visibility_off : Icons.visibility,
    //         size: getResponsiveIconSize(context),
    //         color: _getBorderColor(),
    //       ),

    //       onPressed: _toggleVisibility,
    //       splashColor: Colors.transparent, // removes splash ripple
    //       highlightColor: Colors.transparent, // removes highlight on press
    //       hoverColor: Colors.transparent, // removes hover color on web
    //       focusColor: Colors.transparent, // removes blue outline on focus
    //       autofocus: false, // don't auto-focus
    //       enableFeedback: false, // optional: removes haptic
    //     ),
    //   );
    // }

    if (widget.showToggleVisibility) {
      suffixIcons.add(
        IconButton(
          icon: SvgPicture.asset(
            _obscureText
                ? 'assets/images/visibility-off.svg'
                : 'assets/images/visibility-on.svg',
            package: 'ui_controls_library',
          ),
          onPressed: _toggleVisibility,
          tooltip: _obscureText ? 'Show' : 'Hide',
        ),
      );
    }

    // Create the suffix widget
    Widget? suffixWidget;
    if (suffixIcons.isNotEmpty) {
      suffixWidget = Row(mainAxisSize: MainAxisSize.min, children: suffixIcons);
    }

    // Create the text field
    Widget textField = MouseRegion(
      onEnter: (_) => _handleHoverChange(true),
      onExit: (_) => _handleHoverChange(false),
      child: Theme(
        data: Theme.of(context).copyWith(
          inputDecorationTheme: const InputDecorationTheme(
            hoverColor: Colors.transparent,
            focusColor: Colors.transparent,
          ),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(widget.borderRadius),
          ),
          //height: _getResponsiveHeight(context),
          height: _getResponsivePasswordPanelHeight(context),
          child: TextField(
            controller: _controller,
            focusNode: _focusNode,
            keyboardType: TextInputType.visiblePassword,
            textAlign: widget.textAlign,
            // style: TextStyle(
            //   fontSize: _getResponsiveValueFontSize(context),
            //   fontWeight: FontWeight.w400,
            //  // color: Color(0xFF999999),
            //   fontFamily: 'Inter',
            // ),
            style: FontManager.getCustomStyle(
              fontFamily: FontManager.fontFamilyInter,
              fontWeight: FontManager.medium,
              color: Color(0xFF333333),
              fontSize: _getResponsiveValueFontSize(context),
            ),
            obscureText: _obscureText,
            obscuringCharacter: widget.obscuringCharacter,
            maxLength: widget.maxLength,
            decoration: InputDecoration(
              constraints: BoxConstraints(
                minHeight: _getResponsiveHeight(context),
              ),
              // labelText: widget.label,
              hintText: widget.hint ?? 'Enter Password',
              // hintStyle: TextStyle(
              //   fontSize: _getResponsiveValueFontSize(context),
              //   fontWeight: FontWeight.w400,
              //   //  color: Color(0xFF999999),
              //   fontFamily: 'Inter',
              // ),
              hintStyle: FontManager.getCustomStyle(
                fontFamily: FontManager.fontFamilyInter,
                fontWeight: FontManager.medium,
                fontSize: _getResponsiveValueFontSize(context),
              ),
              helperText: widget.helperText,
              errorText: _errorText,
              filled: true,
              fillColor:
                  widget.isDisabled
                      ? Colors.grey.shade200
                      : widget.backgroundColor,
              suffixIcon: suffixWidget,
              suffixIconColor: _getBorderColor(),
              //isDense: true,
              contentPadding: _getResponsivePadding(context),
              counterText: '', // Hide character counter
              border:
              // widget.hasBorder
              OutlineInputBorder(
                // borderRadius: BorderRadius.circular(widget.borderRadius),
                borderSide: BorderSide(color: _getBorderColor(), width: 1.0),
              ),

              enabledBorder:
              //  widget.hasBorder
              OutlineInputBorder(
                //  borderRadius: BorderRadius.circular(widget.borderRadius),
                borderSide: BorderSide(color: _getBorderColor(), width: 1.0),
              ),

              focusedBorder:
              //  widget.hasBorder
              OutlineInputBorder(
                // borderRadius: BorderRadius.circular(widget.borderRadius),
                borderSide: BorderSide(color: _getBorderColor(), width: 1.0),
              ),

              errorBorder:
              // widget.hasBorder
              OutlineInputBorder(
                // borderRadius: BorderRadius.circular(widget.borderRadius),
                borderSide: BorderSide(color: Colors.red, width: 1.0),
              ),
            ),
            enabled: !widget.isDisabled && !widget.isReadOnly,
            readOnly: widget.isReadOnly,
            // autofocus: widget.autofocus,
            onChanged: _handleValueChanged,
            onSubmitted: (value) {
              if (widget.onSubmitted != null) {
                widget.onSubmitted!(value);
              }
            },
            enableSuggestions: false,
            autocorrect: false,
          ),
        ),
      ),
    );

    // Combine with suggestions and strength indicator below
    Widget combinedWidget = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.showLabel && widget.label != null) ...[
          Text(
            widget.label!,
            // style: TextStyle(
            //   fontSize: _getResponsiveFontSize(context),
            //   fontWeight: FontWeight.w500,
            // ),
            style: FontManager.getCustomStyle(
              fontFamily: FontManager.fontFamilyInter,
              fontWeight: FontManager.medium,
              fontSize: _getResponsiveFontSize(context),
            ),
          ),
          SizedBox(height: _getResponsiveBoxsize(context)),
        ],
        textField,
        _buildSuggestions(),
        _buildStrengthIndicator(), // Now positioned below the input
      ],
    );

    // No shadow applied - removed the shadow logic completely
    return Container(
      width: widget.width,
      height: widget.height > 0 ? widget.height : null,
      margin: widget.margin,
      child: combinedWidget,
    );
  }

  double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1920) {
      return 16.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 14.0; // Large
    } else if (screenWidth >= 1280) {
      return 12.0; // Medium
    } else {
      return 12.0; // Default for very small screens
    }
  }

  double _getResponsiveHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1920) {
      return 56.0; // Extra Large (>1920px)
    } else if (screenWidth >= 1440) {
      return 48.0; // Large (1440-1920px)
    } else if (screenWidth >= 1280) {
      return 40.0; // Medium (1280-1366px)
    } else if (screenWidth >= 768) {
      return 32.0; // Small (768-1024px)
    } else {
      return 32.0; // Default for very small screens
    }
  }

  EdgeInsets _getResponsivePadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth >= 1440) {
      return const EdgeInsets.symmetric(
        horizontal: 16.0,
        vertical: 0.0,
      ); // Extra Large
    } else if (screenWidth >= 1280) {
      return const EdgeInsets.symmetric(
        horizontal: 12.0,
        vertical: 0.0,
      ); // Large// Large
    } else if (screenWidth >= 768) {
      return const EdgeInsets.symmetric(
        horizontal: 8.0,
        vertical: 0.0,
      ); // Medium// Medium
    } else {
      return const EdgeInsets.symmetric(
        horizontal: 6.0,
        vertical: 1.0,
      ); // Default for very small screens
    }
  }

  double getResponsiveIconSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 24.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 22.0; // Large
    } else if (screenWidth >= 1280) {
      return 20.0; // Medium
    } else if (screenWidth >= 768) {
      return 18.0; // Small
    } else {
      return 18.0; // Extra Small
    }
  }

  double _getResponsiveValueFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 16.0; // Large
    } else if (screenWidth >= 1280) {
      return 14.0; // Medium
    } else {
      return 14.0; // Default for very small screens
    }
  }

  double _getResponsiveBoxsize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 8.0; // Extra Large (>1920px)
    } else if (screenWidth >= 1440) {
      return 8.0; // Large (1440-1920px)
    } else if (screenWidth >= 1280) {
      return 6.0; // Medium (1280-1366px)
    } else if (screenWidth >= 768) {
      return 4.0; // Small (768-1024px)
    } else {
      return 4.0; // Default for very small screens
    }
  }
}

double _getResponsivePasswordPanelHeight(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;
  if (screenWidth > 1920) {
    return 82.0; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 74.0; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 66.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 66.0; // Small (768-1024px)
  } else {
    return 66.0; // Default for very small screens
  }
}

double _getResponsiveValueFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 18.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 16.0; // Large
  } else if (screenWidth >= 1280) {
    return 14.0; // Medium
  } else {
    return 14.0; // Default for very small screens
  }
}
